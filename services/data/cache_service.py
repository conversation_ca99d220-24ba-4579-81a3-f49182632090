import time
import threading
import logging
from typing import Any, Optional, Dict
from collections import OrderedDict

logger = logging.getLogger(__name__)

# TTL values (in seconds)
PRICE_TTL = 30    # 30 seconds
MARKET_DATA_TTL = 300  # 5 minutes

class CacheService:
    """Thread-safe cache service with TTL support."""

    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl
        self.cache: OrderedDict = OrderedDict()
        self.timestamps: Dict[str, float] = {}
        self.lock = threading.RLock()

        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_task, daemon=True)
        self.cleanup_thread.start()

    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self.lock:
            if key not in self.cache:
                return None

            # Check if expired
            if time.time() - self.timestamps[key] > self.ttl:
                del self.cache[key]
                del self.timestamps[key]
                return None

            # Move to end (most recently used)
            self.cache.move_to_end(key)
            return self.cache[key]

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with optional TTL override."""
        with self.lock:
            # Remove oldest items if cache is full
            while len(self.cache) >= self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                del self.timestamps[oldest_key]

            self.cache[key] = value
            self.timestamps[key] = time.time()

    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                del self.timestamps[key]
                return True
            return False

    def clear(self) -> None:
        """Clear all cache."""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()

    def size(self) -> int:
        """Get cache size."""
        with self.lock:
            return len(self.cache)



    def _cleanup_task(self) -> None:
        while True:
            time.sleep(min(60, self.ttl / 2))
            try:
                self._cleanup_expired()
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}")

    def _cleanup_expired(self) -> None:
        current_time = time.time()
        with self.lock:
            expired_keys = [
                key for key, timestamp in self.timestamps.items()
                if current_time - timestamp > self.ttl
            ]

            for key in expired_keys:
                del self.cache[key]
                del self.timestamps[key]

_cache_instances: Dict[str, CacheService] = {}
_cache_lock = threading.RLock()

def get_cache(cache_name: str, max_size: int = 1000, ttl: int = 300) -> CacheService:
    with _cache_lock:
        if cache_name not in _cache_instances:
            _cache_instances[cache_name] = CacheService(max_size, ttl)
        return _cache_instances[cache_name]





_global_cache_service = None

def get_cache_service() -> CacheService:
    global _global_cache_service
    if _global_cache_service is None:
        _global_cache_service = CacheService()
    return _global_cache_service
